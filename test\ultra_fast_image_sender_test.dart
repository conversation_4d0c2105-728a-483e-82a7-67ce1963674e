import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/services/ultra_fast_image_sender.dart';
import 'package:mr_garments_mobile/services/image_send_performance_monitor.dart';
import 'package:mr_garments_mobile/services/background_upload_service.dart';

void main() {
  group('UltraFastImageSender Tests', () {
    setUp(() {
      // Clear any existing performance data
      ImageSendPerformanceMonitor.instance.clearAll();
    });

    test('should create FastSendResult with correct properties', () {
      final result = FastSendResult(
        success: true,
        processedCount: 10,
        totalCount: 10,
        sentMessageIds: ['msg1', 'msg2', 'msg3'],
        failedImages: [],
      );

      expect(result.success, isTrue);
      expect(result.processedCount, equals(10));
      expect(result.totalCount, equals(10));
      expect(result.sentMessageIds.length, equals(3));
      expect(result.failedImages.isEmpty, isTrue);
      expect(result.successRate, equals(0.3)); // 3/10
    });

    test('should calculate success rate correctly', () {
      final result = FastSendResult(
        success: true,
        processedCount: 100,
        totalCount: 100,
        sentMessageIds: List.generate(95, (i) => 'msg$i'),
        failedImages: ['failed1', 'failed2', 'failed3', 'failed4', 'failed5'],
      );

      expect(result.successRate, equals(0.95)); // 95/100
    });

    test('should handle zero total count', () {
      final result = FastSendResult(
        success: false,
        processedCount: 0,
        totalCount: 0,
        sentMessageIds: [],
        failedImages: [],
      );

      expect(result.successRate, equals(0.0));
    });

    test('should create LocalProcessResult correctly', () {
      final result = LocalProcessResult(
        success: true,
        messageId: 'test_message_123',
        localImagePath: '/path/to/image.jpg',
        thumbnailPath: '/path/to/thumb.jpg',
        index: 5,
      );

      expect(result.success, isTrue);
      expect(result.messageId, equals('test_message_123'));
      expect(result.localImagePath, equals('/path/to/image.jpg'));
      expect(result.thumbnailPath, equals('/path/to/thumb.jpg'));
      expect(result.index, equals(5));
      expect(result.error, isNull);
    });

    test('should create LocalProcessResult with error', () {
      final result = LocalProcessResult(
        success: false,
        messageId: 'test_message_456',
        error: 'Failed to process image',
        index: 2,
      );

      expect(result.success, isFalse);
      expect(result.messageId, equals('test_message_456'));
      expect(result.error, equals('Failed to process image'));
      expect(result.localImagePath, isNull);
      expect(result.thumbnailPath, isNull);
      expect(result.index, equals(2));
    });

    test('should get processing statistics', () {
      final stats = UltraFastImageSender.getProcessingStats();

      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('uploadQueue'), isTrue);
      expect(stats.containsKey('activeUploads'), isTrue);
      expect(stats.containsKey('totalPending'), isTrue);
      expect(stats.containsKey('maxConcurrent'), isTrue);
      expect(stats['maxConcurrent'], equals(12)); // uploadBatchSize
    });
  });

  group('Performance Monitor Tests', () {
    test('should start and complete session', () {
      final monitor = ImageSendPerformanceMonitor.instance;

      final sessionId = monitor.startSession(
        totalImages: 50,
        chatId: 'test_chat_123',
        sessionName: 'Test Session',
      );

      expect(sessionId, isNotEmpty);
      expect(sessionId.startsWith('session_'), isTrue);

      // Update progress
      monitor.updateProgress(
        sessionId: sessionId,
        processedImages: 25,
        uploadedImages: 20,
        failedImages: 2,
      );

      // Get session stats
      final stats = monitor.getSessionStats(sessionId);
      expect(stats, isNotNull);
      expect(stats!.totalImages, equals(50));
      expect(stats.processedImages, equals(25));
      expect(stats.uploadedImages, equals(20));
      expect(stats.failedImages, equals(2));
      expect(stats.progressPercentage, equals(50.0)); // 25/50 * 100

      // Complete session
      monitor.completeSession(sessionId, success: true);

      final completedStats = monitor.getSessionStats(sessionId);
      expect(completedStats!.isCompleted, isTrue);
      expect(completedStats.isSuccessful, isTrue);
    });

    test('should calculate overall statistics', () {
      final monitor = ImageSendPerformanceMonitor.instance;
      monitor.clearAll();

      // Create and complete a few sessions
      final session1 = monitor.startSession(
        totalImages: 10,
        chatId: 'chat1',
        sessionName: 'Session 1',
      );
      monitor.updateProgress(
        sessionId: session1,
        processedImages: 10,
        uploadedImages: 10,
      );
      monitor.completeSession(session1, success: true);

      final session2 = monitor.startSession(
        totalImages: 20,
        chatId: 'chat2',
        sessionName: 'Session 2',
      );
      monitor.updateProgress(
        sessionId: session2,
        processedImages: 18,
        uploadedImages: 18,
      );
      monitor.completeSession(session2, success: true);

      final overallStats = monitor.getOverallStats();
      expect(overallStats.totalSessions, equals(2));
      expect(overallStats.totalImagesProcessed, equals(28)); // 10 + 18
      expect(overallStats.successRate, equals(1.0)); // Both successful
    });

    test('should handle empty statistics', () {
      final monitor = ImageSendPerformanceMonitor.instance;
      monitor.clearAll();

      final overallStats = monitor.getOverallStats();
      expect(overallStats.totalSessions, equals(0));
      expect(overallStats.totalImagesProcessed, equals(0));
      expect(overallStats.successRate, equals(0.0));
      expect(overallStats.averageImagesPerMinute, equals(0.0));
    });

    test('should track active sessions', () {
      final monitor = ImageSendPerformanceMonitor.instance;
      monitor.clearAll();

      final sessionId = monitor.startSession(
        totalImages: 100,
        chatId: 'active_chat',
        sessionName: 'Active Session',
      );

      final activeSessions = monitor.getActiveSessions();
      expect(activeSessions.length, equals(1));
      expect(activeSessions.first.sessionId, equals(sessionId));
      expect(activeSessions.first.isCompleted, isFalse);

      monitor.completeSession(sessionId);

      final activeSessionsAfter = monitor.getActiveSessions();
      expect(activeSessionsAfter.length, equals(0));
    });
  });

  group('Background Upload Service Tests', () {
    test('should have correct configuration', () {
      expect(BackgroundUploadService.maxConcurrentUploads, equals(12));
      expect(BackgroundUploadService.batchSize, equals(6));
    });

    test('should get upload statistics', () {
      final service = BackgroundUploadService.instance;
      final stats = service.getUploadStats();

      expect(stats, isA<Map<String, int>>());
      expect(stats.containsKey('queued'), isTrue);
      expect(stats.containsKey('active'), isTrue);
      expect(stats.containsKey('total'), isTrue);
      expect(stats['queued'], isA<int>());
      expect(stats['active'], isA<int>());
      expect(stats['total'], isA<int>());
    });

    test('should clear all uploads', () async {
      final service = BackgroundUploadService.instance;
      await service.clearAllUploads();

      final stats = service.getUploadStats();
      expect(stats['queued'], equals(0));
      expect(stats['active'], equals(0));
      expect(stats['total'], equals(0));
    });
  });

  group('Integration Tests', () {
    test('should handle empty image list gracefully', () async {
      final result = await UltraFastImageSender.sendImagesUltraFast(
        chatId: 'test_chat',
        images: [],
      );

      expect(result.success, isFalse);
      expect(result.error, equals('No images to send'));
      expect(result.processedCount, equals(0));
      expect(result.totalCount, equals(0));
    });

    test('should validate performance constants', () {
      expect(UltraFastImageSender.maxParallelProcessing, equals(8));
      expect(UltraFastImageSender.uploadBatchSize, equals(12));
      expect(
        UltraFastImageSender.maxParallelProcessing,
        lessThanOrEqualTo(UltraFastImageSender.uploadBatchSize),
      );
    });
  });
}
