import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/local_storage_service.dart';
import 'package:mr_garments_mobile/services/background_upload_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/image_send_performance_monitor.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';

/// Ultra-fast image sender optimized for sending 100+ images in under 1 minute
/// Uses parallel processing, optimized compression, and batch uploads
class UltraFastImageSender {
  static UltraFastImageSender? _instance;
  static UltraFastImageSender get instance =>
      _instance ??= UltraFastImageSender._();
  UltraFastImageSender._();

  static const int maxParallelProcessing = 8; // Process 8 images simultaneously
  static const int uploadBatchSize = 12; // Upload 12 images concurrently

  /// Send images with ultra-fast processing
  static Future<FastSendResult> sendImagesUltraFast({
    required String chatId,
    required List<ProcessedImage> images,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Function(int processed, int total)? onProgress,
    Function(String messageId, String status)? onStatusUpdate,
  }) async {
    if (images.isEmpty) {
      return FastSendResult(
        success: false,
        error: 'No images to send',
        processedCount: 0,
        totalCount: 0,
      );
    }

    // Start performance monitoring
    late final String sessionId;

    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      final totalImages = images.length;
      int processedCount = 0;
      final List<String> sentMessageIds = [];
      final List<String> failedImages = [];

      debugPrint('Starting ultra-fast send for $totalImages images');

      // Initialize performance monitoring
      sessionId = ImageSendPerformanceMonitor.instance.startSession(
        totalImages: totalImages,
        chatId: chatId,
        sessionName: 'Ultra-Fast Send ($totalImages images)',
      );

      // Step 1: Process images in parallel batches for local storage
      final localStorageTasks = <Future<LocalProcessResult>>[];

      for (int i = 0; i < images.length; i += maxParallelProcessing) {
        final batch = images.skip(i).take(maxParallelProcessing).toList();

        for (int j = 0; j < batch.length; j++) {
          final image = batch[j];
          final messageId =
              'ultra_${DateTime.now().millisecondsSinceEpoch}_${i + j}';

          localStorageTasks.add(
            _processImageForLocalStorage(
              image: image,
              messageId: messageId,
              chatId: chatId,
              index: i + j,
            ),
          );
        }

        // Process batch and wait for completion
        if (localStorageTasks.length >= maxParallelProcessing ||
            i + maxParallelProcessing >= images.length) {
          final batchResults = await Future.wait(localStorageTasks);
          localStorageTasks.clear();

          processedCount += batchResults.length;
          onProgress?.call(processedCount, totalImages);

          // Update performance monitoring
          ImageSendPerformanceMonitor.instance.updateProgress(
            sessionId: sessionId,
            processedImages: processedCount,
          );

          // Queue successful results for upload
          final uploadTasks = <UploadTask>[];

          for (final result in batchResults) {
            if (result.success) {
              final uploadTask = UploadTask(
                messageId: result.messageId,
                chatId: chatId,
                localImagePath: result.localImagePath!,
                thumbnailPath: result.thumbnailPath,
                replyToMessageId: result.index == 0 ? replyToMessageId : null,
                replyToText: result.index == 0 ? replyToText : null,
                replyToSenderName: result.index == 0 ? replyToSenderName : null,
                metadata: {
                  'batchIndex': result.index,
                  'batchTotal': totalImages,
                  'ultraFastMode': true,
                },
                onProgress: (progress) {
                  onStatusUpdate?.call(
                    result.messageId,
                    progress.status.displayText,
                  );
                },
                onSuccess: (uploadUrl) {
                  sentMessageIds.add(result.messageId);
                  onStatusUpdate?.call(result.messageId, 'Sent');
                },
                onError: (error) {
                  failedImages.add(result.messageId);
                  onStatusUpdate?.call(result.messageId, 'Failed');
                },
              );

              uploadTasks.add(uploadTask);
            } else {
              failedImages.add(result.messageId);
            }
          }

          // Queue uploads in batches
          if (uploadTasks.isNotEmpty) {
            await BackgroundUploadService.instance.queueBatchUploads(
              uploadTasks,
            );
          }
        }
      }

      debugPrint(
        'Ultra-fast send completed: ${sentMessageIds.length} queued for upload',
      );

      // Complete performance monitoring
      ImageSendPerformanceMonitor.instance.completeSession(
        sessionId,
        success: failedImages.isEmpty,
      );

      return FastSendResult(
        success: true,
        processedCount: processedCount,
        totalCount: totalImages,
        sentMessageIds: sentMessageIds,
        failedImages: failedImages,
      );
    } catch (e) {
      debugPrint('Ultra-fast send error: $e');

      // Complete performance monitoring with error
      ImageSendPerformanceMonitor.instance.completeSession(
        sessionId,
        success: false,
      );

      return FastSendResult(
        success: false,
        error: e.toString(),
        processedCount: 0,
        totalCount: images.length,
      );
    }
  }

  /// Process single image for local storage
  static Future<LocalProcessResult> _processImageForLocalStorage({
    required ProcessedImage image,
    required String messageId,
    required String chatId,
    required int index,
  }) async {
    try {
      final result = await LocalStorageService.storeImageLocally(
        sourceFile: image.compressedFile,
        messageId: messageId,
        chatId: chatId,
        thumbnailSize: 150, // Smaller thumbnails for speed
        imageQuality: 75, // Balanced quality/speed
      );

      if (result.success) {
        return LocalProcessResult(
          success: true,
          messageId: messageId,
          localImagePath: result.localImagePath,
          thumbnailPath: result.thumbnailPath,
          index: index,
        );
      } else {
        return LocalProcessResult(
          success: false,
          messageId: messageId,
          error: result.error,
          index: index,
        );
      }
    } catch (e) {
      return LocalProcessResult(
        success: false,
        messageId: messageId,
        error: e.toString(),
        index: index,
      );
    }
  }

  /// Get current processing statistics
  static Map<String, dynamic> getProcessingStats() {
    final uploadStats = BackgroundUploadService.instance.getUploadStats();
    return {
      'uploadQueue': uploadStats['queued'],
      'activeUploads': uploadStats['active'],
      'totalPending': uploadStats['total'],
      'maxConcurrent': uploadBatchSize,
    };
  }
}

/// Result model for ultra-fast sending
class FastSendResult {
  final bool success;
  final String? error;
  final int processedCount;
  final int totalCount;
  final List<String> sentMessageIds;
  final List<String> failedImages;

  FastSendResult({
    required this.success,
    this.error,
    required this.processedCount,
    required this.totalCount,
    this.sentMessageIds = const [],
    this.failedImages = const [],
  });

  double get successRate =>
      totalCount > 0 ? sentMessageIds.length / totalCount : 0.0;
}

/// Local processing result model
class LocalProcessResult {
  final bool success;
  final String messageId;
  final String? localImagePath;
  final String? thumbnailPath;
  final String? error;
  final int index;

  LocalProcessResult({
    required this.success,
    required this.messageId,
    this.localImagePath,
    this.thumbnailPath,
    this.error,
    required this.index,
  });
}
