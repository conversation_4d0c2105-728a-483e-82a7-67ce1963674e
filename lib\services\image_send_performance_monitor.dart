import 'package:flutter/material.dart';

/// Performance monitoring service for image sending operations
/// Tracks metrics like processing time, upload speed, and success rates
class ImageSendPerformanceMonitor {
  static ImageSendPerformanceMonitor? _instance;
  static ImageSendPerformanceMonitor get instance =>
      _instance ??= ImageSendPerformanceMonitor._();
  ImageSendPerformanceMonitor._();

  final Map<String, SendingSession> _activeSessions = {};
  final List<SendingSession> _completedSessions = [];

  /// Start monitoring a new sending session
  String startSession({
    required int totalImages,
    required String chatId,
    String? sessionName,
  }) {
    final sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';
    final session = SendingSession(
      sessionId: sessionId,
      chatId: chatId,
      totalImages: totalImages,
      sessionName: sessionName ?? 'Image Send Session',
      startTime: DateTime.now(),
    );

    _activeSessions[sessionId] = session;
    debugPrint('Started monitoring session: $sessionId ($totalImages images)');
    
    return sessionId;
  }

  /// Update session progress
  void updateProgress({
    required String sessionId,
    int? processedImages,
    int? uploadedImages,
    int? failedImages,
  }) {
    final session = _activeSessions[sessionId];
    if (session == null) return;

    if (processedImages != null) {
      session.processedImages = processedImages;
    }
    if (uploadedImages != null) {
      session.uploadedImages = uploadedImages;
    }
    if (failedImages != null) {
      session.failedImages = failedImages;
    }

    session.lastUpdateTime = DateTime.now();
  }

  /// Mark session as completed
  void completeSession(String sessionId, {bool success = true}) {
    final session = _activeSessions.remove(sessionId);
    if (session == null) return;

    session.endTime = DateTime.now();
    session.isCompleted = true;
    session.isSuccessful = success;

    _completedSessions.add(session);
    
    // Keep only last 50 sessions to prevent memory leaks
    if (_completedSessions.length > 50) {
      _completedSessions.removeAt(0);
    }

    _logSessionResults(session);
  }

  /// Get current session statistics
  SessionStats? getSessionStats(String sessionId) {
    final session = _activeSessions[sessionId] ?? 
        _completedSessions.firstWhere(
          (s) => s.sessionId == sessionId,
          orElse: () => throw StateError('Session not found'),
        );

    return SessionStats.fromSession(session);
  }

  /// Get overall performance statistics
  OverallStats getOverallStats() {
    final allSessions = [..._activeSessions.values, ..._completedSessions];
    
    if (allSessions.isEmpty) {
      return OverallStats(
        totalSessions: 0,
        totalImagesProcessed: 0,
        averageProcessingTime: Duration.zero,
        successRate: 0.0,
        averageImagesPerMinute: 0.0,
      );
    }

    final completedSessions = allSessions.where((s) => s.isCompleted).toList();
    final totalImages = allSessions.fold<int>(0, (sum, s) => sum + s.totalImages);
    final totalProcessed = allSessions.fold<int>(0, (sum, s) => sum + s.processedImages);
    
    final totalDuration = completedSessions.fold<Duration>(
      Duration.zero,
      (sum, s) => sum + (s.endTime?.difference(s.startTime) ?? Duration.zero),
    );

    final successfulSessions = completedSessions.where((s) => s.isSuccessful).length;
    final successRate = completedSessions.isNotEmpty 
        ? successfulSessions / completedSessions.length 
        : 0.0;

    final averageProcessingTime = completedSessions.isNotEmpty
        ? Duration(milliseconds: totalDuration.inMilliseconds ~/ completedSessions.length)
        : Duration.zero;

    final averageImagesPerMinute = totalDuration.inMinutes > 0
        ? totalProcessed / totalDuration.inMinutes
        : 0.0;

    return OverallStats(
      totalSessions: allSessions.length,
      totalImagesProcessed: totalProcessed,
      averageProcessingTime: averageProcessingTime,
      successRate: successRate,
      averageImagesPerMinute: averageImagesPerMinute,
    );
  }

  /// Get active sessions
  List<SessionStats> getActiveSessions() {
    return _activeSessions.values
        .map((session) => SessionStats.fromSession(session))
        .toList();
  }

  /// Clear all monitoring data
  void clearAll() {
    _activeSessions.clear();
    _completedSessions.clear();
    debugPrint('Cleared all performance monitoring data');
  }

  /// Log session results for debugging
  void _logSessionResults(SendingSession session) {
    final duration = session.endTime!.difference(session.startTime);
    final imagesPerMinute = duration.inMinutes > 0 
        ? session.uploadedImages / duration.inMinutes 
        : 0.0;

    debugPrint('''
=== Image Send Performance Report ===
Session: ${session.sessionName}
Total Images: ${session.totalImages}
Processed: ${session.processedImages}
Uploaded: ${session.uploadedImages}
Failed: ${session.failedImages}
Duration: ${duration.inMinutes}m ${duration.inSeconds % 60}s
Speed: ${imagesPerMinute.toStringAsFixed(1)} images/minute
Success Rate: ${((session.uploadedImages / session.totalImages) * 100).toStringAsFixed(1)}%
Status: ${session.isSuccessful ? 'SUCCESS' : 'FAILED'}
=====================================
''');
  }
}

/// Represents a single image sending session
class SendingSession {
  final String sessionId;
  final String chatId;
  final String sessionName;
  final int totalImages;
  final DateTime startTime;
  
  DateTime? endTime;
  DateTime lastUpdateTime;
  int processedImages = 0;
  int uploadedImages = 0;
  int failedImages = 0;
  bool isCompleted = false;
  bool isSuccessful = false;

  SendingSession({
    required this.sessionId,
    required this.chatId,
    required this.sessionName,
    required this.totalImages,
    required this.startTime,
  }) : lastUpdateTime = DateTime.now();

  Duration get elapsedTime => 
      (endTime ?? DateTime.now()).difference(startTime);

  double get progressPercentage => 
      totalImages > 0 ? (processedImages / totalImages) * 100 : 0.0;

  double get uploadPercentage => 
      totalImages > 0 ? (uploadedImages / totalImages) * 100 : 0.0;
}

/// Statistics for a specific session
class SessionStats {
  final String sessionId;
  final String sessionName;
  final int totalImages;
  final int processedImages;
  final int uploadedImages;
  final int failedImages;
  final Duration elapsedTime;
  final double progressPercentage;
  final double uploadPercentage;
  final bool isCompleted;
  final bool isSuccessful;

  SessionStats({
    required this.sessionId,
    required this.sessionName,
    required this.totalImages,
    required this.processedImages,
    required this.uploadedImages,
    required this.failedImages,
    required this.elapsedTime,
    required this.progressPercentage,
    required this.uploadPercentage,
    required this.isCompleted,
    required this.isSuccessful,
  });

  factory SessionStats.fromSession(SendingSession session) {
    return SessionStats(
      sessionId: session.sessionId,
      sessionName: session.sessionName,
      totalImages: session.totalImages,
      processedImages: session.processedImages,
      uploadedImages: session.uploadedImages,
      failedImages: session.failedImages,
      elapsedTime: session.elapsedTime,
      progressPercentage: session.progressPercentage,
      uploadPercentage: session.uploadPercentage,
      isCompleted: session.isCompleted,
      isSuccessful: session.isSuccessful,
    );
  }

  double get imagesPerMinute => 
      elapsedTime.inMinutes > 0 ? uploadedImages / elapsedTime.inMinutes : 0.0;

  String get formattedDuration {
    final minutes = elapsedTime.inMinutes;
    final seconds = elapsedTime.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }
}

/// Overall performance statistics
class OverallStats {
  final int totalSessions;
  final int totalImagesProcessed;
  final Duration averageProcessingTime;
  final double successRate;
  final double averageImagesPerMinute;

  OverallStats({
    required this.totalSessions,
    required this.totalImagesProcessed,
    required this.averageProcessingTime,
    required this.successRate,
    required this.averageImagesPerMinute,
  });

  String get formattedAverageTime {
    final minutes = averageProcessingTime.inMinutes;
    final seconds = averageProcessingTime.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }

  String get formattedSuccessRate => '${(successRate * 100).toStringAsFixed(1)}%';
}
