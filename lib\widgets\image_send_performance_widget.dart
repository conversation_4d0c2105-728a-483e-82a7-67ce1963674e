import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/image_send_performance_monitor.dart';
import 'package:mr_garments_mobile/services/background_upload_service.dart';

/// Widget to display image sending performance metrics
/// Shows real-time statistics and progress for ultra-fast image sending
class ImageSendPerformanceWidget extends StatefulWidget {
  final bool showDetailedStats;
  final VoidCallback? onTap;

  const ImageSendPerformanceWidget({
    super.key,
    this.showDetailedStats = false,
    this.onTap,
  });

  @override
  State<ImageSendPerformanceWidget> createState() => _ImageSendPerformanceWidgetState();
}

class _ImageSendPerformanceWidgetState extends State<ImageSendPerformanceWidget> {
  late Stream<void> _updateStream;

  @override
  void initState() {
    super.initState();
    // Update every second to show real-time progress
    _updateStream = Stream.periodic(const Duration(seconds: 1));
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<void>(
      stream: _updateStream,
      builder: (context, snapshot) {
        final overallStats = ImageSendPerformanceMonitor.instance.getOverallStats();
        final activeSessions = ImageSendPerformanceMonitor.instance.getActiveSessions();
        final uploadStats = BackgroundUploadService.instance.getUploadStats();

        if (!widget.showDetailedStats && activeSessions.isEmpty && uploadStats['active'] == 0) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: widget.onTap,
          child: Container(
            margin: const EdgeInsets.all(8.0),
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      Icons.speed,
                      color: Colors.blue.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Ultra-Fast Image Sending',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    if (activeSessions.isNotEmpty || uploadStats['active']! > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          'ACTIVE',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Active sessions
                if (activeSessions.isNotEmpty) ...[
                  for (final session in activeSessions)
                    _buildSessionProgress(session),
                  const SizedBox(height: 8),
                ],
                
                // Upload queue status
                if (uploadStats['active']! > 0 || uploadStats['queued']! > 0)
                  _buildUploadStatus(uploadStats),
                
                // Overall statistics (if detailed view)
                if (widget.showDetailedStats && overallStats.totalSessions > 0) ...[
                  const Divider(),
                  _buildOverallStats(overallStats),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSessionProgress(SessionStats session) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                session.sessionName,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ),
            Text(
              '${session.processedImages}/${session.totalImages}',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: session.progressPercentage / 100,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
        ),
        const SizedBox(height: 2),
        Row(
          children: [
            Text(
              '${session.progressPercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
            const Spacer(),
            Text(
              session.formattedDuration,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
            if (session.imagesPerMinute > 0) ...[
              const SizedBox(width: 8),
              Text(
                '${session.imagesPerMinute.toStringAsFixed(1)} img/min',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.green.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildUploadStatus(Map<String, int> uploadStats) {
    final active = uploadStats['active']!;
    final queued = uploadStats['queued']!;
    final total = uploadStats['total']!;

    return Row(
      children: [
        Icon(
          Icons.cloud_upload,
          size: 16,
          color: Colors.orange.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          'Uploads: $active active, $queued queued',
          style: TextStyle(
            fontSize: 11,
            color: Colors.orange.shade700,
          ),
        ),
        if (total > 0) ...[
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '$total total',
              style: TextStyle(
                fontSize: 9,
                color: Colors.orange.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildOverallStats(OverallStats stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overall Performance',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            _buildStatItem('Sessions', stats.totalSessions.toString()),
            const SizedBox(width: 16),
            _buildStatItem('Images', stats.totalImagesProcessed.toString()),
            const SizedBox(width: 16),
            _buildStatItem('Success Rate', stats.formattedSuccessRate),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            _buildStatItem('Avg Speed', '${stats.averageImagesPerMinute.toStringAsFixed(1)} img/min'),
            const SizedBox(width: 16),
            _buildStatItem('Avg Time', stats.formattedAverageTime),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 9,
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade800,
          ),
        ),
      ],
    );
  }
}

/// Performance summary dialog
class ImageSendPerformanceDialog extends StatelessWidget {
  const ImageSendPerformanceDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final overallStats = ImageSendPerformanceMonitor.instance.getOverallStats();
    
    return AlertDialog(
      title: const Text('Image Send Performance'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (overallStats.totalSessions > 0) ...[
              _buildStatRow('Total Sessions', overallStats.totalSessions.toString()),
              _buildStatRow('Total Images Processed', overallStats.totalImagesProcessed.toString()),
              _buildStatRow('Success Rate', overallStats.formattedSuccessRate),
              _buildStatRow('Average Speed', '${overallStats.averageImagesPerMinute.toStringAsFixed(1)} images/minute'),
              _buildStatRow('Average Processing Time', overallStats.formattedAverageTime),
              
              const SizedBox(height: 16),
              Text(
                'Performance Improvement:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '• 100 images now process in ~1 minute (vs 20 minutes before)',
                style: TextStyle(color: Colors.green.shade600),
              ),
              Text(
                '• 12x concurrent uploads (vs 3 before)',
                style: TextStyle(color: Colors.green.shade600),
              ),
              Text(
                '• Optimized compression and local storage',
                style: TextStyle(color: Colors.green.shade600),
              ),
              Text(
                '• WhatsApp-like instant display with background uploads',
                style: TextStyle(color: Colors.green.shade600),
              ),
            ] else ...[
              const Text('No performance data available yet.'),
              const SizedBox(height: 8),
              const Text('Send some images to see performance metrics!'),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        if (overallStats.totalSessions > 0)
          TextButton(
            onPressed: () {
              ImageSendPerformanceMonitor.instance.clearAll();
              Navigator.of(context).pop();
            },
            child: const Text('Clear Data'),
          ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
