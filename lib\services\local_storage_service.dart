import 'dart:io';
// import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter_image_compress/flutter_image_compress.dart';

/// Local storage service for WhatsApp-like image management
/// Handles local file storage, thumbnails, and cache management
class LocalStorageService {
  static LocalStorageService? _instance;
  static LocalStorageService get instance =>
      _instance ??= LocalStorageService._();
  LocalStorageService._();

  static Directory? _appDocumentsDir;
  static Directory? _chatImagesDir;
  static Directory? _thumbnailsDir;
  static Directory? _tempDir;

  /// Initialize local storage directories
  static Future<void> initialize() async {
    try {
      _appDocumentsDir = await getApplicationDocumentsDirectory();

      // Create chat images directory
      _chatImagesDir = Directory(
        path.join(_appDocumentsDir!.path, 'chat_images'),
      );
      if (!await _chatImagesDir!.exists()) {
        await _chatImagesDir!.create(recursive: true);
      }

      // Create thumbnails directory
      _thumbnailsDir = Directory(
        path.join(_appDocumentsDir!.path, 'thumbnails'),
      );
      if (!await _thumbnailsDir!.exists()) {
        await _thumbnailsDir!.create(recursive: true);
      }

      // Create temp directory
      _tempDir = Directory(path.join(_appDocumentsDir!.path, 'temp'));
      if (!await _tempDir!.exists()) {
        await _tempDir!.create(recursive: true);
      }

      // Clean up old files on initialization
      await _cleanupOldFiles();

      debugPrint('LocalStorageService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing LocalStorageService: $e');
      rethrow;
    }
  }

  /// Store image locally and generate thumbnail with optimizations
  static Future<LocalImageResult> storeImageLocally({
    required File sourceFile,
    required String messageId,
    required String chatId,
    int thumbnailSize = 150, // Smaller for faster generation
    int imageQuality = 75, // Balanced quality/speed
  }) async {
    try {
      if (_chatImagesDir == null || _thumbnailsDir == null) {
        await initialize();
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(sourceFile.path).toLowerCase();
      final fileName = '${chatId}_${messageId}_$timestamp$extension';

      // Check if file already exists (avoid duplicate processing)
      final localImagePath = path.join(_chatImagesDir!.path, fileName);
      final thumbnailFileName = 'thumb_$fileName';
      final thumbnailPath = path.join(_thumbnailsDir!.path, thumbnailFileName);

      if (await File(localImagePath).exists() &&
          await File(thumbnailPath).exists()) {
        // Return existing files
        final imageSize = await File(localImagePath).length();
        final thumbSize = await File(thumbnailPath).length();

        return LocalImageResult(
          localImagePath: localImagePath,
          thumbnailPath: thumbnailPath,
          imageSize: imageSize,
          thumbnailSize: thumbSize,
          success: true,
        );
      }

      // Process image and thumbnail in parallel for speed
      final futures = await Future.wait([
        _compressImage(sourceFile, localImagePath, quality: imageQuality),
        Future.delayed(Duration.zero), // Placeholder for parallel processing
      ]);

      final compressedFile = futures[0] as File;

      // Generate thumbnail after compression
      final thumbnailFile = await _generateThumbnail(
        compressedFile,
        thumbnailPath,
        size: thumbnailSize,
      );

      // Get file sizes
      final imageSize = await compressedFile.length();
      final thumbSize = await thumbnailFile.length();

      return LocalImageResult(
        localImagePath: compressedFile.path,
        thumbnailPath: thumbnailFile.path,
        imageSize: imageSize,
        thumbnailSize: thumbSize,
        success: true,
      );
    } catch (e) {
      debugPrint('Error storing image locally: $e');
      return LocalImageResult(success: false, error: e.toString());
    }
  }

  /// Compress image for local storage with optimized settings
  static Future<File> _compressImage(
    File sourceFile,
    String targetPath, {
    int quality = 75, // Reduced for faster compression
  }) async {
    try {
      // Get file size to determine compression strategy
      final fileSize = await sourceFile.length();

      // Skip compression for small files (< 500KB)
      if (fileSize < 500 * 1024) {
        return await sourceFile.copy(targetPath);
      }

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        sourceFile.path,
        targetPath,
        quality: quality,
        minWidth: 800, // Reduced for faster processing
        minHeight: 800,
        format: CompressFormat.jpeg,
        keepExif: false,
        rotate: 0, // Disable auto-rotation for speed
      );

      if (compressedFile != null) {
        return File(compressedFile.path);
      } else {
        // If compression fails, copy original file
        return await sourceFile.copy(targetPath);
      }
    } catch (e) {
      // Fallback to copying original file
      return await sourceFile.copy(targetPath);
    }
  }

  /// Generate thumbnail for quick preview with optimized settings
  static Future<File> _generateThumbnail(
    File sourceFile,
    String thumbnailPath, {
    int size = 150, // Smaller for faster generation
  }) async {
    try {
      final thumbnailFile = await FlutterImageCompress.compressAndGetFile(
        sourceFile.path,
        thumbnailPath,
        quality: 50, // Lower quality for thumbnails
        minWidth: size,
        minHeight: size,
        format: CompressFormat.jpeg,
        keepExif: false,
        rotate: 0, // Disable auto-rotation for speed
      );

      if (thumbnailFile != null) {
        return File(thumbnailFile.path);
      } else {
        // If thumbnail generation fails, copy original
        return await sourceFile.copy(thumbnailPath);
      }
    } catch (e) {
      // Fallback to copying original file
      return await sourceFile.copy(thumbnailPath);
    }
  }

  /// Check if local file exists
  static Future<bool> localFileExists(String? localPath) async {
    if (localPath == null || localPath.isEmpty) return false;
    try {
      final file = File(localPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Get local file if it exists
  static Future<File?> getLocalFile(String? localPath) async {
    if (await localFileExists(localPath)) {
      return File(localPath!);
    }
    return null;
  }

  /// Batch store multiple images for ultra-fast processing
  static Future<List<LocalImageResult>> storeImagesBatch({
    required List<File> sourceFiles,
    required List<String> messageIds,
    required String chatId,
    int thumbnailSize = 150,
    int imageQuality = 75,
    int maxConcurrent = 6,
  }) async {
    final results = <LocalImageResult>[];

    // Process in batches for optimal performance
    for (int i = 0; i < sourceFiles.length; i += maxConcurrent) {
      final batch = sourceFiles.skip(i).take(maxConcurrent).toList();
      final batchIds = messageIds.skip(i).take(maxConcurrent).toList();

      final futures = <Future<LocalImageResult>>[];
      for (int j = 0; j < batch.length; j++) {
        futures.add(
          storeImageLocally(
            sourceFile: batch[j],
            messageId: batchIds[j],
            chatId: chatId,
            thumbnailSize: thumbnailSize,
            imageQuality: imageQuality,
          ),
        );
      }

      final batchResults = await Future.wait(futures);
      results.addAll(batchResults);
    }

    return results;
  }

  /// Delete local image and thumbnail
  static Future<bool> deleteLocalImage(
    String? localPath,
    String? thumbnailPath,
  ) async {
    try {
      bool success = true;

      if (localPath != null && await localFileExists(localPath)) {
        await File(localPath).delete();
      }

      if (thumbnailPath != null && await localFileExists(thumbnailPath)) {
        await File(thumbnailPath).delete();
      }

      return success;
    } catch (e) {
      debugPrint('Error deleting local image: $e');
      return false;
    }
  }

  /// Clean up old files (older than 30 days)
  static Future<void> _cleanupOldFiles() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      // Clean chat images
      if (_chatImagesDir != null && await _chatImagesDir!.exists()) {
        await _cleanupDirectory(_chatImagesDir!, cutoffDate);
      }

      // Clean thumbnails
      if (_thumbnailsDir != null && await _thumbnailsDir!.exists()) {
        await _cleanupDirectory(_thumbnailsDir!, cutoffDate);
      }

      // Clean temp files
      if (_tempDir != null && await _tempDir!.exists()) {
        await _cleanupDirectory(_tempDir!, cutoffDate);
      }

      debugPrint('Local storage cleanup completed');
    } catch (e) {
      debugPrint('Error during cleanup: $e');
    }
  }

  /// Clean up files in a directory older than cutoff date
  static Future<void> _cleanupDirectory(
    Directory dir,
    DateTime cutoffDate,
  ) async {
    try {
      final files = dir.listSync();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning directory ${dir.path}: $e');
    }
  }

  /// Get cache size information
  static Future<CacheInfo> getCacheInfo() async {
    try {
      int totalSize = 0;
      int fileCount = 0;

      if (_chatImagesDir != null && await _chatImagesDir!.exists()) {
        final result = await _getDirectorySize(_chatImagesDir!);
        totalSize += result.size;
        fileCount += result.count;
      }

      if (_thumbnailsDir != null && await _thumbnailsDir!.exists()) {
        final result = await _getDirectorySize(_thumbnailsDir!);
        totalSize += result.size;
        fileCount += result.count;
      }

      return CacheInfo(
        totalSize: totalSize,
        fileCount: fileCount,
        formattedSize: _formatBytes(totalSize),
      );
    } catch (e) {
      return CacheInfo(totalSize: 0, fileCount: 0, formattedSize: '0 B');
    }
  }

  /// Get directory size and file count
  static Future<DirectoryInfo> _getDirectorySize(Directory dir) async {
    int size = 0;
    int count = 0;

    try {
      final files = dir.listSync(recursive: true);
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          size += stat.size;
          count++;
        }
      }
    } catch (e) {
      debugPrint('Error getting directory size: $e');
    }

    return DirectoryInfo(size: size, count: count);
  }

  /// Format bytes to human readable string
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Clear all cached files
  static Future<bool> clearAllCache() async {
    try {
      if (_chatImagesDir != null && await _chatImagesDir!.exists()) {
        await _chatImagesDir!.delete(recursive: true);
        await _chatImagesDir!.create(recursive: true);
      }

      if (_thumbnailsDir != null && await _thumbnailsDir!.exists()) {
        await _thumbnailsDir!.delete(recursive: true);
        await _thumbnailsDir!.create(recursive: true);
      }

      if (_tempDir != null && await _tempDir!.exists()) {
        await _tempDir!.delete(recursive: true);
        await _tempDir!.create(recursive: true);
      }

      return true;
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      return false;
    }
  }
}

/// Result of storing an image locally
class LocalImageResult {
  final String? localImagePath;
  final String? thumbnailPath;
  final int? imageSize;
  final int? thumbnailSize;
  final bool success;
  final String? error;

  LocalImageResult({
    this.localImagePath,
    this.thumbnailPath,
    this.imageSize,
    this.thumbnailSize,
    required this.success,
    this.error,
  });
}

/// Cache information
class CacheInfo {
  final int totalSize;
  final int fileCount;
  final String formattedSize;

  CacheInfo({
    required this.totalSize,
    required this.fileCount,
    required this.formattedSize,
  });
}

/// Directory information
class DirectoryInfo {
  final int size;
  final int count;

  DirectoryInfo({required this.size, required this.count});
}
